# VxeTable 组件修复说明

## 问题描述
当启用自适应页面大小功能时，如果计算出的自适应页面大小恰好等于某个固定选项的值（如20），会导致分页器中同时选中两个选项的问题。

## 问题原因
原来的实现中，自适应选项的 `value` 直接使用计算出的数字值，这可能与固定选项的 `value` 冲突。

## 解决方案
使用特殊的字符串标识符来区分自适应选项：

1. **初始化时**：自适应选项的 `value` 设置为 `'adaptive_10'`
2. **动态更新时**：自适应选项的 `value` 设置为 `'adaptive_${计算出的页面大小}'`
3. **选择处理时**：检测到自适应选项被选中时，提取实际的页面大小值传递给父组件

## 修改的文件
- `src/components/VxeTable/index.vue`
- `src/components/VxeTable/permission.vue`

## 修改内容

### 1. 初始化 pageSize 数组
```javascript
// 修改前
pageSize.unshift({ label: '自适应', value: 10 })

// 修改后
pageSize.unshift({ label: '自适应', value: 'adaptive_10' })
```

### 2. dealSize 方法
```javascript
// 修改前
this.pageSize = this.pageSize.map((el, i) => (i === 0 ? { label: '自适应', value: this.adaptPageSize } : el))

// 修改后
this.pageSize = this.pageSize.map((el, i) => (i === 0 ? { label: '自适应', value: `adaptive_${this.adaptPageSize}` } : el))
```

### 3. onPageChange 方法
```javascript
// 修改前
if (page.type === 'size' && obj.currentOption?.label === '自适应') {
  this.isAdaptPageSizeData = true
  this.dealSize()
  return
}

// 修改后
const isAdaptiveSelected = page.type === 'size' && (
  obj.currentOption?.label === '自适应' || 
  (typeof page.pageSize === 'string' && page.pageSize.startsWith('adaptive_'))
)

if (isAdaptiveSelected) {
  this.isAdaptPageSizeData = true
  this.dealSize()
  // 将实际的页面大小传递给父组件
  const actualPageSize = typeof page.pageSize === 'string' ? 
    parseInt(page.pageSize.replace('adaptive_', '')) : 
    this.adaptPageSize
  this.$listeners.handlePageChange({ ...page, pageSize: actualPageSize })
  return
}
```

## 测试建议
1. 启用自适应页面大小功能
2. 调整窗口大小，使计算出的自适应页面大小等于某个固定选项（如20）
3. 验证分页器中只有一个选项被选中
4. 验证切换选项时功能正常
